/*
  # Convert assets table to snapshot pattern

  1. Changes
    - Make unit_price nullable to support deletion snapshots
    - Add asset_identifier to group snapshots of the same logical asset
    - Add snapshot_type to distinguish between different types of snapshots
    - Keep all existing columns for backward compatibility
    
  2. Snapshot Pattern
    - Each change creates a new row instead of updating existing ones
    - Deletions are represented as snapshots with quantity = 0 and unit_price = NULL
    - Latest snapshot per asset_identifier represents current state
    
  3. Security
    - RLS policies remain unchanged as they are table-level
*/

-- Add new columns for snapshot pattern
ALTER TABLE assets 
  ADD COLUMN asset_identifier uuid,
  ADD COLUMN snapshot_type text NOT NULL DEFAULT 'update';

-- Make unit_price nullable for deletion snapshots
ALTER TABLE assets 
  ALTER COLUMN unit_price DROP NOT NULL;

-- For existing records, set asset_identifier to their current id
-- This ensures backward compatibility
UPDATE assets 
SET asset_identifier = id 
WHERE asset_identifier IS NULL;

-- Make asset_identifier NOT NULL after setting values
ALTER TABLE assets 
  ALTER COLUMN asset_identifier SET NOT NULL;

-- Add index for efficient querying of latest snapshots
CREATE INDEX idx_assets_identifier_created_at 
  ON assets (asset_identifier, created_at DESC);

-- Add index for user queries
CREATE INDEX idx_assets_user_identifier 
  ON assets (user_id, asset_identifier);

-- Add check constraint for snapshot_type
ALTER TABLE assets 
  ADD CONSTRAINT check_snapshot_type 
  CHECK (snapshot_type IN ('create', 'update', 'delete'));

-- Add check constraint for deletion snapshots
ALTER TABLE assets 
  ADD CONSTRAINT check_deletion_snapshot 
  CHECK (
    (snapshot_type = 'delete' AND quantity = 0 AND unit_price IS NULL) OR
    (snapshot_type != 'delete' AND unit_price IS NOT NULL)
  );

-- Create a view for getting latest asset snapshots
CREATE OR REPLACE VIEW latest_assets AS
SELECT DISTINCT ON (asset_identifier) 
  id,
  asset_identifier,
  bucket_id,
  name,
  symbol,
  type,
  quantity,
  unit_price,
  currency_symbol,
  snapshot_type,
  created_at,
  user_id
FROM assets 
ORDER BY asset_identifier, created_at DESC;

-- Grant access to the view
GRANT SELECT ON latest_assets TO authenticated;

-- Note: Views inherit RLS from underlying tables
-- The latest_assets view will automatically respect the RLS policies on the assets table
