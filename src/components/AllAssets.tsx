import React from 'react';
import { Link } from 'react-router-dom';

interface Asset {
  id: string;
  name: string;
  symbol: string;
  type: string;
  quantity: number;
  unit_price: number;
  created_at: string;
}

interface AllAssetsProps {
  assets: Asset[];
  currency: 'USD' | 'EUR' | 'GBP';
  loading: boolean;
}

export const AllAssets: React.FC<AllAssetsProps> = ({ assets, currency, loading }) => {
  const currencySymbols = {
    USD: '$',
    EUR: '€',
    GBP: '£'
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black"></div>
      </div>
    );
  }

  if (assets.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No assets found. Add your first asset to get started.</p>
      </div>
    );
  }

  return (
    <div className="overflow-hidden">
      <div className="max-h-96 overflow-y-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50 sticky top-0">
            <tr>
              <th
                scope="col"
                className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider"
              >
                Asset
              </th>
              <th
                scope="col"
                className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider"
              >
                Type
              </th>
              <th
                scope="col"
                className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider"
              >
                Value
              </th>
              <th scope="col" className="relative px-4 py-3">
                <span className="sr-only">View</span>
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {assets.map((asset) => (
              <tr key={asset.id} className="hover:bg-gray-50 transition-colors duration-150">
                <td className="px-4 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div>
                      <div className="text-sm font-medium text-black">{asset.name}</div>
                      <div className="text-sm text-gray-500">{asset.symbol}</div>
                    </div>
                  </div>
                </td>
                <td className="px-4 py-4 whitespace-nowrap">
                  <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                    {asset.type}
                  </span>
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-black">
                  {currencySymbols[currency]}{(asset.quantity * asset.unit_price).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <Link to={`/asset/${asset.id}`} className="text-black hover:text-gray-600">
                    View
                  </Link>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};
