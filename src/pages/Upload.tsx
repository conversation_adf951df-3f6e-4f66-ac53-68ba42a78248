import React, { useState, useEffect } from 'react';
import { Upload as UploadIcon, Plus, X, FileText, Save, AlertTriangle, Trash2 } from 'lucide-react';
import { supabase } from '../utils/supabaseClient';
import { useNavigate } from 'react-router-dom';

interface Bucket {
  id: string;
  name: string;
  file: File | null;
  isProcessing: boolean;
}

interface Asset {
  id: string;
  asset_identifier: string;
  bucket_id: string;
  name: string;
  symbol: string;
  type: string;
  quantity: number;
  unit_price: number | null;
  currency_symbol: string;
  snapshot_type: 'create' | 'update' | 'delete';
  newQuantity: number;
  newUnitPrice: number;
  markedForDeletion?: boolean;
}

export const Upload: React.FC = () => {
  const navigate = useNavigate();
  const [buckets, setBuckets] = useState<Bucket[]>([]);
  const [assets, setAssets] = useState<Asset[]>([]);
  const [editingBucketId, setEditingBucketId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      
      if (error || !user) {
        navigate('/login');
        return;
      }

      setIsAuthenticated(true);
      fetchBuckets();
      fetchAssets();
    } catch (error) {
      console.error('Error checking auth status:', error);
      navigate('/login');
    } finally {
      setLoading(false);
    }
  };

  const fetchBuckets = async () => {
    try {
      const { data, error } = await supabase
        .from('buckets')
        .select('*')
        .order('created_at', { ascending: true });

      if (error) throw error;

      setBuckets(data.map(bucket => ({
        ...bucket,
        file: null,
        isProcessing: false
      })));
    } catch (error) {
      console.error('Error fetching buckets:', error);
    }
  };

  const fetchAssets = async () => {
    try {
      const { data, error } = await supabase
        .from('latest_assets')
        .select('*')
        .neq('snapshot_type', 'delete')
        .order('created_at', { ascending: true });

      if (error) throw error;

      setAssets(data.map(asset => ({
        ...asset,
        newQuantity: asset.quantity,
        newUnitPrice: asset.unit_price || 0,
        currency_symbol: asset.currency_symbol || '$'
      })));
    } catch (error) {
      console.error('Error fetching assets:', error);
    }
  };

  const handleAssetChange = (id: string, field: keyof Asset, value: string) => {
    setAssets(prevAssets =>
      prevAssets.map(asset =>
        asset.id === id
          ? {
              ...asset,
              [field]: ['newQuantity', 'newUnitPrice'].includes(field)
                ? parseFloat(value) || 0
                : value
            }
          : asset
      )
    );
  };

  const handleDeleteAsset = (asset: Asset) => {
    if (asset.quantity === 0 && asset.unit_price === 0) {
      setAssets(prevAssets => prevAssets.filter(a => a.id !== asset.id));
    } else {
      setAssets(prevAssets =>
        prevAssets.map(a =>
          a.id === asset.id
            ? { ...a, markedForDeletion: !a.markedForDeletion }
            : a
        )
      );
    }
  };

  const addNewAsset = (bucketId: string) => {
    const assetIdentifier = crypto.randomUUID();
    const newAsset: Asset = {
      id: crypto.randomUUID(),
      asset_identifier: assetIdentifier,
      bucket_id: bucketId,
      name: '',
      symbol: '',
      type: '',
      quantity: 0,
      unit_price: 0,
      currency_symbol: '$',
      snapshot_type: 'create',
      newQuantity: 0,
      newUnitPrice: 0
    };
    setAssets([...assets, newAsset]);
  };

  const handleSaveAssets = async () => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    setIsSaving(true);
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;
      if (!user) {
        navigate('/login');
        return;
      }

      const snapshots = [];

      // Handle updates and new assets
      const updates = assets.filter(asset =>
        !asset.markedForDeletion &&
        (asset.newQuantity !== asset.quantity ||
        asset.newUnitPrice !== asset.unit_price ||
        asset.quantity === 0)
      );

      for (const asset of updates) {
        snapshots.push({
          asset_identifier: asset.asset_identifier,
          bucket_id: asset.bucket_id,
          name: asset.name,
          symbol: asset.symbol,
          type: asset.type,
          quantity: asset.newQuantity,
          unit_price: asset.newUnitPrice,
          currency_symbol: asset.currency_symbol,
          snapshot_type: asset.quantity === 0 ? 'create' : 'update',
          user_id: user.id
        });
      }

      // Handle deletions as snapshots with quantity = 0 and unit_price = NULL
      const deletions = assets.filter(asset => asset.markedForDeletion);
      for (const asset of deletions) {
        snapshots.push({
          asset_identifier: asset.asset_identifier,
          bucket_id: asset.bucket_id,
          name: asset.name,
          symbol: asset.symbol,
          type: asset.type,
          quantity: 0,
          unit_price: null,
          currency_symbol: asset.currency_symbol,
          snapshot_type: 'delete',
          user_id: user.id
        });
      }

      // Insert all snapshots
      if (snapshots.length > 0) {
        const { error: insertError } = await supabase
          .from('assets')
          .insert(snapshots);

        if (insertError) throw insertError;
      }

      await fetchAssets();
    } catch (error) {
      console.error('Error saving assets:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleFileUpload = async (bucketId: string, file: File) => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    setBuckets(buckets.map(bucket =>
      bucket.id === bucketId ? { ...bucket, file, isProcessing: true } : bucket
    ));

    try {
      const mockAssets = [
        {
          bucket_id: bucketId,
          name: 'Microsoft Corp',
          symbol: 'MSFT',
          type: 'Stock',
          newQuantity: 75,
          newUnitPrice: 351,
          currency_symbol: '$'
        },
        {
          bucket_id: bucketId,
          name: 'Apple Inc',
          symbol: 'AAPL',
          type: 'Stock',
          newQuantity: 100,
          newUnitPrice: 175.50,
          currency_symbol: '$'
        },
        {
          bucket_id: bucketId,
          name: 'Bitcoin',
          symbol: 'BTC',
          type: 'Cryptocurrency',
          newQuantity: 2,
          newUnitPrice: 45000.00,
          currency_symbol: '$'
        }
      ];

      const currentBucketAssets = assets.filter(asset => asset.bucket_id === bucketId);
      
      const updatedAssets = assets.map(asset => {
        if (asset.bucket_id === bucketId) {
          const mockAsset = mockAssets.find(mock => mock.name === asset.name);
          return {
            ...asset,
            markedForDeletion: !mockAsset,
            newQuantity: mockAsset ? mockAsset.newQuantity : asset.newQuantity,
            newUnitPrice: mockAsset ? mockAsset.newUnitPrice : asset.newUnitPrice,
            currency_symbol: mockAsset ? mockAsset.currency_symbol : asset.currency_symbol
          };
        }
        return asset;
      });

      mockAssets.forEach(mockAsset => {
        const exists = currentBucketAssets.some(asset => asset.name === mockAsset.name);
        if (!exists) {
          updatedAssets.push({
            id: crypto.randomUUID(),
            asset_identifier: crypto.randomUUID(),
            ...mockAsset,
            quantity: 0,
            unit_price: 0,
            snapshot_type: 'create' as const,
          });
        }
      });

      setAssets(updatedAssets);
    } catch (error) {
      console.error('Error processing file:', error);
    } finally {
      setBuckets(buckets.map(bucket =>
        bucket.id === bucketId ? { ...bucket, isProcessing: false } : bucket
      ));
    }
  };

  const createBucket = async () => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    if (buckets.length >= 10) {
      alert('Maximum 10 buckets allowed');
      return;
    }

    try {
      const {
        data: { user },
        error: userError
      } = await supabase.auth.getUser();

      if (userError) throw userError;
      if (!user) {
        navigate('/login');
        return;
      }

      const { data, error } = await supabase
        .from('buckets')
        .insert([
          { 
            name: `Bucket ${buckets.length + 1}`,
            user_id: user.id
          }
        ])
        .select()
        .single();

      if (error) throw error;

      setBuckets([...buckets, { ...data, file: null, isProcessing: false }]);
    } catch (error) {
      console.error('Error creating bucket:', error);
    }
  };

  const updateBucketName = async (id: string, name: string) => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    try {
      const { error } = await supabase
        .from('buckets')
        .update({ name })
        .eq('id', id);

      if (error) throw error;

      setBuckets(buckets.map(bucket => 
        bucket.id === id ? { ...bucket, name } : bucket
      ));
    } catch (error) {
      console.error('Error updating bucket name:', error);
    }
  };

  const removeBucket = async (id: string) => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    try {
      const { error } = await supabase
        .from('buckets')
        .delete()
        .eq('id', id);

      if (error) throw error;

      setBuckets(buckets.filter(bucket => bucket.id !== id));
      setAssets(assets.filter(asset => asset.bucket_id !== id));
    } catch (error) {
      console.error('Error removing bucket:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading...</div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="space-y-6">
      <div>
        <div className="flex justify-between items-center mb-4">
          <div>
            <h2 className="text-lg font-medium text-gray-900">Upload Assets</h2>
            <p className="mt-1 text-sm text-gray-500">Select a bucket to upload your assets</p>
          </div>
          <button
            onClick={createBucket}
            disabled={buckets.length >= 10}
            className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            <Plus className="h-4 w-4 mr-1" />
            New Bucket
          </button>
        </div>

        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {buckets.map(bucket => (
            <div key={bucket.id} className="bg-white rounded-lg shadow-sm border border-gray-200 hover:border-blue-500 transition-colors">
              <div className="p-3">
                <div className="flex justify-between items-center mb-2">
                  {editingBucketId === bucket.id ? (
                    <input
                      type="text"
                      value={bucket.name}
                      onChange={(e) => updateBucketName(bucket.id, e.target.value)}
                      onBlur={() => setEditingBucketId(null)}
                      autoFocus
                      className="w-full text-sm border-gray-300 rounded-md"
                    />
                  ) : (
                    <h3
                      className="text-sm font-medium text-gray-900 cursor-pointer hover:text-blue-600 truncate"
                      onClick={() => setEditingBucketId(bucket.id)}
                      title={bucket.name}
                    >
                      {bucket.name}
                    </h3>
                  )}
                  <button
                    onClick={() => removeBucket(bucket.id)}
                    className="text-gray-400 hover:text-red-600 ml-2"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>

                {bucket.file ? (
                  <div className="text-xs bg-gray-50 rounded p-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center truncate">
                        <FileText className="h-3 w-3 text-gray-400 mr-1 flex-shrink-0" />
                        <span className="text-gray-600 truncate" title={bucket.file.name}>
                          {bucket.file.name}
                        </span>
                      </div>
                      {!bucket.isProcessing && (
                        <button
                          onClick={() => setBuckets(buckets.map(b =>
                            b.id === bucket.id ? { ...b, file: null } : b
                          ))}
                          className="text-red-600 hover:text-red-800 ml-2"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      )}
                    </div>
                    {bucket.isProcessing && (
                      <div className="text-xs text-blue-600 mt-1">Processing...</div>
                    )}
                  </div>
                ) : (
                  <div
                    onClick={() => document.getElementById(`file-upload-${bucket.id}`)?.click()}
                    className="cursor-pointer text-center py-2 border border-dashed border-gray-300 rounded hover:border-blue-500 transition-colors"
                  >
                    <UploadIcon className="h-4 w-4 text-gray-400 mx-auto" />
                    <input
                      type="file"
                      id={`file-upload-${bucket.id}`}
                      className="hidden"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) handleFileUpload(bucket.id, file);
                      }}
                    />
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      <div>
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-medium text-gray-900">Asset List</h2>
          <div className="flex space-x-4">
            <button
              onClick={() => buckets[0] && addNewAsset(buckets[0].id)}
              disabled={buckets.length === 0}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Asset
            </button>
            <button
              onClick={handleSaveAssets}
              disabled={isSaving}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              <Save className="h-4 w-4 mr-2" />
              {isSaving ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </div>

        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            {assets.map((asset) => {
              const isNew = asset.quantity === 0 && asset.unit_price === 0;
              const isUpdated = !asset.markedForDeletion && !isNew && 
                (asset.newQuantity !== asset.quantity || asset.newUnitPrice !== asset.unit_price);
              
              let statusBg = 'bg-white';
              let statusText = '';
              if (asset.markedForDeletion) {
                statusBg = 'bg-red-50';
                statusText = 'Marked for deletion';
              } else if (isNew) {
                statusBg = 'bg-green-50';
                statusText = 'New asset';
              } else if (isUpdated) {
                statusBg = 'bg-yellow-50';
                statusText = 'Updated';
              }

              return (
                <div 
                  key={asset.id} 
                  className={`${statusBg} border-b border-gray-200 p-4`}
                >
                  <div className="grid grid-cols-12 gap-4">
                    <div className="col-span-3">
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500">Bucket</label>
                        <select
                          value={asset.bucket_id}
                          onChange={(e) => handleAssetChange(asset.id, 'bucket_id', e.target.value)}
                          className="block w-full text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                          disabled={!isNew}
                        >
                          {buckets.map(bucket => (
                            <option key={bucket.id} value={bucket.id}>{bucket.name}</option>
                          ))}
                        </select>
                      </div>
                    </div>

                    <div className="col-span-3">
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500">Asset Name</label>
                        <div className="flex items-center">
                          {asset.markedForDeletion && (
                            <AlertTriangle className="h-4 w-4 text-red-500 mr-2" />
                          )}
                          <input
                            type="text"
                            value={asset.name}
                            onChange={(e) => handleAssetChange(asset.id, 'name', e.target.value)}
                            className="block w-full text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                            disabled={!isNew}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="col-span-2">
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500">Symbol</label>
                        <input
                          type="text"
                          value={asset.symbol}
                          onChange={(e) => handleAssetChange(asset.id, 'symbol', e.target.value)}
                          className="block w-full text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                          disabled={!isNew}
                        />
                      </div>
                    </div>

                    <div className="col-span-2">
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500">Type</label>
                        <select
                          value={asset.type}
                          onChange={(e) => handleAssetChange(asset.id, 'type', e.target.value)}
                          className="block w-full text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                          disabled={!isNew}
                        >
                          <option value="">Select Type</option>
                          <option value="Stock">Stock</option>
                          <option value="Cryptocurrency">Cryptocurrency</option>
                          <option value="Real Estate">Real Estate</option>
                          <option value="Bonds">Bonds</option>
                          <option value="Commodities">Commodities</option>
                        </select>
                      </div>
                    </div>

                    <div className="col-span-2">
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500">Currency</label>
                        <select
                          value={asset.currency_symbol}
                          onChange={(e) => handleAssetChange(asset.id, 'currency_symbol', e.target.value)}
                          className="block w-full text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                          disabled={!isNew}
                        >
                          <option value="$">$ (USD)</option>
                          <option value="€">€ (EUR)</option>
                          <option value="£">£ (GBP)</option>
                          <option value="¥">¥ (JPY)</option>
                          <option value="A$">A$ (AUD)</option>
                          <option value="₹">₹ (INR)</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-12 gap-4 mt-4">
                    <div className="col-span-2">
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500">Current Qty</label>
                        <p className="text-sm text-gray-700">{asset.quantity || '-'}</p>
                      </div>
                    </div>

                    <div className="col-span-2">
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500">Current Price</label>
                        <p className="text-sm text-gray-700">
                          {asset.unit_price ? `${asset.currency_symbol}${asset.unit_price.toFixed(2)}` : '-'}
                        </p>
                      </div>
                    </div>

                    <div className="col-span-2">
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500">New Qty</label>
                        <input
                          type="number"
                          min="0"
                          step="1"
                          value={asset.newQuantity}
                          onChange={(e) => handleAssetChange(asset.id, 'newQuantity', e.target.value)}
                          className="block w-full text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                          disabled={asset.markedForDeletion}
                        />
                      </div>
                    </div>

                    <div className="col-span-2">
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500">New Price</label>
                        <input
                          type="number"
                          min="0"
                          step="0.01"
                          value={asset.newUnitPrice}
                          onChange={(e) => handleAssetChange(asset.id, 'newUnitPrice', e.target.value)}
                          className="block w-full text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                          disabled={asset.markedForDeletion}
                        />
                      </div>
                    </div>

                    <div className="col-span-2">
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500">Total Value</label>
                        <p className="text-sm font-medium text-gray-900">
                          {asset.currency_symbol}{(asset.newQuantity * asset.newUnitPrice).toFixed(2)}
                        </p>
                      </div>
                    </div>

                    <div className="col-span-2 flex items-end justify-between">
                      <div className="text-sm">
                        {statusText && (
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            asset.markedForDeletion ? 'bg-red-100 text-red-800' :
                            isNew ? 'bg-green-100 text-green-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {statusText}
                          </span>
                        )}
                      </div>
                      <button
                        onClick={() => handleDeleteAsset(asset)}
                        className={`text-red-600 hover:text-red-900 ${
                          asset.markedForDeletion ? 'opacity-50' : ''
                        }`}
                      >
                        <Trash2 className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};